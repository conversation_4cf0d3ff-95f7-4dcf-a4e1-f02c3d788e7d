import {request} from '@/api/index'


export default {
    //合同履行计划新增
    addContractPerform(data) {
        return request({
            url: '/bm-contract-perform/save',
            method: 'post',
            data
        })
    },
    //根据合同id 查询对应的履行计划
    queryByContractId(data) {
        return request({
            url: '/bm-contract-perform/queryByContractId',
            method: 'post',
            data
        })
    },
    //更新履行计划
    updateContractPerform(data) {
        return request({
            url: '/bm-contract-perform/update',
            method: 'post',
            data

        })
    },
    //删除履行计划
    deleteContractPerform(data) {
        return request({
            url: '/bm-contract-perform/delete',
            method: 'post',
            data:data
        })
    },
    //删除履行计划
    submitPerform(data) {
        return request({
            url: '/bm-contract-perform/submitPerform',
            method: 'post',
            data:data
        })
    },
    //删除履行计划
    queryByContractCode(data) {
        return request({
            url: '/bm-contract-perform/queryByContractCode',
            method: 'post',
            data:data
        })
    },
    createScheduler(data){
        return request({
            url: '/bm-contract-perform/createScheduler',
            method: 'post',
            data:data
        })
    },
    loadPayPerfomList(data){
        return request({
            url: '/bm-contract-perform/loadPerformList',
            method: 'post',
            data:data
        })
    },
    exportContractPerformDataList(data){
        return request({
            url: '/bm-contract-perform/exportContractPerformDataList',
            method: 'post',
            data:data,
            responseType: 'blob'
        })
    }


}